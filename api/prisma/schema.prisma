// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
}

model User {
  id        String    @id @default(cuid())
  email     String    @unique
  password  String
  role      Role      @default(USER)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  account   Account?
  sessions  Session[]
  watched   Watched[]
  lists     List[]

  @@map("user")
}

model Account {
  id        String   @id @default(cuid())
  userId    String   @unique
  username  String?  @unique
  name      String?
  image     String?
  about     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Media {
  id        String     @id @default(cuid())
  tmdbId    Int        @unique
  title     String
  type      MediaType
  posterUrl String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  watched   Watched[]
  listItems ListItem[]

  @@map("media")
}

model Watched {
  id          String   @id @default(cuid())
  userId      String
  mediaId     String
  dateWatched DateTime @default(now())
  rating      Float?
  review      String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  media       Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([userId, mediaId])
  @@map("watched")
}

model List {
  id          String     @id @default(cuid())
  userId      String
  name        String
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       ListItem[]

  @@map("list")
}

model ListItem {
  id        String   @id @default(cuid())
  listId    String
  mediaId   String
  createdAt DateTime @default(now())
  list      List     @relation(fields: [listId], references: [id], onDelete: Cascade)
  media     Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([listId, mediaId])
  @@map("list_item")
}

enum MediaType {
  MOVIE
  TV_SHOW
}
