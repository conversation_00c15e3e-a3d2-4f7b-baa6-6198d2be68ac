import { IsString, IsOptional, <PERSON>N<PERSON>ber, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class WatchedDto {
  @ApiProperty({ example: 'media-id' })
  @IsString()
  mediaId: string;

  @ApiProperty({ example: '2023-12-01T00:00:00Z', required: false })
  @IsOptional()
  @IsDateString()
  dateWatched: string;

  @ApiProperty({ example: 8.5, minimum: 0, maximum: 10, required: false })
  @IsOptional()
  @IsNumber()
  rating?: number;

  @ApiProperty({ example: 'Great movie!', required: false })
  @IsOptional()
  @IsString()
  review?: string;
}
