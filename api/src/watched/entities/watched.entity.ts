import { MediaType } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class WatchedItem {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  mediaId: string;

  @ApiProperty()
  dateWatched: Date;

  @ApiProperty({ required: false })
  rating?: number;

  @ApiProperty({ required: false })
  review?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class WatchedWithMedia extends WatchedItem {
  @ApiProperty()
  media: {
    id: string;
    tmdbId: number;
    title: string;
    type: MediaType;
    posterUrl?: string;
  };
}

export class WatchedStats {
  @ApiProperty()
  totalWatched: number;

  @ApiProperty()
  totalMovies: number;

  @ApiProperty()
  totalTVShows: number;

  @ApiProperty()
  averageRating: number;

  @ApiProperty()
  thisMonth: number;

  @ApiProperty()
  thisYear: number;
}
