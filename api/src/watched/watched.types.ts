import { MediaType } from '@prisma/client';

export interface WatchedItem {
  id: string;
  userId: string;
  mediaId: string;
  watchDate: Date;
  rating?: number;
  review?: string;
  createdAt: Date;
  updatedAt: Date;
  media: {
    id: string;
    tmdbId: number;
    title: string;
    type: MediaType;
    posterUrl?: string;
  };
}

export interface WatchedStats {
  totalWatched: number;
  totalMovies: number;
  totalTVShows: number;
  averageRating: number;
  watchedThisWeek: number;
  watchedThisMonth: number;
}

export interface WatchedFilters {
  type?: MediaType;
  year?: number;
  rating?: {
    min?: number;
    max?: number;
  };
  dateRange?: {
    start?: Date;
    end?: Date;
  };
}
