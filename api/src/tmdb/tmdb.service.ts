import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  TMDBMovie,
  TMDBTVShow,
  TMDBResponse,
  TMDBMovieDetails,
  TMDBTVShowDetails,
  TMDBGenresResponse,
} from './tmdb.types';

@Injectable()
export class TmdbService {
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly imageBaseUrl: string;

  constructor(private configService: ConfigService) {
    this.baseUrl =
      this.configService.get<string>('TMDB_BASE_URL') ||
      'https://api.themoviedb.org/3';
    this.apiKey = this.configService.get<string>('TMDB_API_KEY')!;
    this.imageBaseUrl =
      this.configService.get<string>('TMDB_IMAGE_BASE_URL') ||
      'https://image.tmdb.org/t/p';

    if (!this.apiKey) {
      throw new Error('TMDB_API_KEY is required');
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    params: Record<string, any> = {},
  ): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    // Add API key and other params
    url.searchParams.append('api_key', this.apiKey);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value.toString());
      }
    });

    try {
      const response = await fetch(url.toString(), {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new HttpException(
          `TMDB API Error: ${response.statusText}`,
          response.status,
        );
      }

      return response.json();
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to fetch data from TMDB',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private getPosterUrl(posterPath: string | null): string | null {
    return posterPath ? `${this.imageBaseUrl}/w500${posterPath}` : null;
  }

  async getNewReleases(page: number = 1): Promise<TMDBResponse<TMDBMovie>> {
    return this.makeRequest<TMDBResponse<TMDBMovie>>('/movie/now_playing', {
      page,
    });
  }

  async getTrending(
    mediaType: 'movie' | 'tv' = 'movie',
    timeWindow: 'day' | 'week' = 'week',
    page: number = 1,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    return this.makeRequest<TMDBResponse<TMDBMovie | TMDBTVShow>>(
      `/trending/${mediaType}/${timeWindow}`,
      { page },
    );
  }

  async getUpcoming(page: number = 1): Promise<TMDBResponse<TMDBMovie>> {
    return this.makeRequest<TMDBResponse<TMDBMovie>>('/movie/upcoming', {
      page,
    });
  }

  async getPopular(
    mediaType: 'movie' | 'tv' = 'movie',
    page: number = 1,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    return this.makeRequest<TMDBResponse<TMDBMovie | TMDBTVShow>>(
      `/${mediaType}/popular`,
      { page },
    );
  }

  async search(
    query: string,
    page: number = 1,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    return this.makeRequest<TMDBResponse<TMDBMovie | TMDBTVShow>>(
      '/search/multi',
      {
        query: encodeURIComponent(query),
        page,
      },
    );
  }

  async getMovieDetails(movieId: number): Promise<TMDBMovieDetails> {
    return this.makeRequest(`/movie/${movieId}`);
  }

  async getTVShowDetails(tvId: number): Promise<TMDBTVShowDetails> {
    return this.makeRequest(`/tv/${tvId}`);
  }

  async getGenres(
    mediaType: 'movie' | 'tv' = 'movie',
  ): Promise<TMDBGenresResponse> {
    return this.makeRequest(`/genre/${mediaType}/list`);
  }

  async getByGenre(
    genreId: number,
    mediaType: 'movie' | 'tv' = 'movie',
    page: number = 1,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    return this.makeRequest<TMDBResponse<TMDBMovie | TMDBTVShow>>(
      `/discover/${mediaType}`,
      {
        with_genres: genreId,
        page,
      },
    );
  }
}

// Re-export types for backward compatibility
export type {
  TMDBMovie,
  TMDBTVShow,
  TMDBResponse,
  TMDBMovieDetails,
  TMDBTVShowDetails,
  TMDBGenresResponse,
} from './tmdb.types';
