import { ApiProperty } from '@nestjs/swagger';

export class TMDBMovieDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  title: string;

  @ApiProperty({ nullable: true })
  poster_path: string | null;

  @ApiProperty()
  overview: string;

  @ApiProperty()
  release_date: string;

  @ApiProperty()
  vote_average: number;

  @ApiProperty({ type: [Number] })
  genre_ids: number[];
}

export class TMDBTVShowDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty({ nullable: true })
  poster_path: string | null;

  @ApiProperty()
  overview: string;

  @ApiProperty()
  first_air_date: string;

  @ApiProperty()
  vote_average: number;

  @ApiProperty({ type: [Number] })
  genre_ids: number[];
}

export class TMDBResponseDto<T> {
  @ApiProperty({ isArray: true })
  results: T[];

  @ApiProperty()
  total_pages: number;

  @ApiProperty()
  total_results: number;

  @ApiProperty()
  page: number;
}

export class TMDBGenreDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;
}

export class TMDBMovieDetailsDto extends TMDBMovieDto {
  @ApiProperty()
  runtime: number;

  @ApiProperty({ type: [TMDBGenreDto] })
  genres: TMDBGenreDto[];
}

export class TMDBTVShowDetailsDto extends TMDBTVShowDto {
  @ApiProperty()
  number_of_seasons: number;

  @ApiProperty({ type: [TMDBGenreDto] })
  genres: TMDBGenreDto[];
}

export class TMDBGenresResponseDto {
  @ApiProperty({ type: [TMDBGenreDto] })
  genres: TMDBGenreDto[];
}
