import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsN<PERSON>ber, IsEnum, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { MediaType } from '@prisma/client';

export class CreateMediaDto {
  @ApiProperty({ example: 12345 })
  @IsNumber()
  tmdbId: number;

  @ApiProperty({ example: 'The Matrix' })
  @IsString()
  title: string;

  @ApiProperty({ enum: MediaType, example: MediaType.MOVIE })
  @IsEnum(MediaType)
  type: MediaType;

  @ApiProperty({
    example: 'https://image.tmdb.org/t/p/w500/abc123.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  posterUrl?: string;
}
