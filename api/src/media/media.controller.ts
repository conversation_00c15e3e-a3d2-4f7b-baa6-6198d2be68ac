import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { MediaService } from './media.service';
import { Public } from '../auth/auth.decorators';
import { TMDBMovie, TMDBTVShow, TMDBResponse } from '../tmdb/tmdb.types';

@ApiTags('media')
@Controller('media')
@Public() // Make all media routes public
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Get('new-releases')
  @ApiOperation({ summary: 'Get new movie releases from TMDB' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'New releases retrieved successfully',
  })
  async getNewReleases(
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie>> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    return this.mediaService.getNewReleases(pageNumber);
  }

  @Get('trending')
  @ApiOperation({ summary: 'Get trending movies and TV shows from TMDB' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['movie', 'tv'],
    description: 'Media type',
  })
  @ApiQuery({
    name: 'time_window',
    required: false,
    enum: ['day', 'week'],
    description: 'Time window',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'Trending content retrieved successfully',
  })
  async getTrending(
    @Query('type') type?: 'movie' | 'tv',
    @Query('time_window') timeWindow?: 'day' | 'week',
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    const mediaType = type || 'movie';
    const window = timeWindow || 'week';
    const pageNumber = page ? parseInt(page, 10) : 1;
    return this.mediaService.getTrending(mediaType, window, pageNumber);
  }

  @Get('upcoming')
  @ApiOperation({ summary: 'Get upcoming movies from TMDB' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'Upcoming movies retrieved successfully',
  })
  async getUpcoming(
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie>> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    return this.mediaService.getUpcoming(pageNumber);
  }

  @Get('popular')
  @ApiOperation({ summary: 'Get popular movies and TV shows from TMDB' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['movie', 'tv'],
    description: 'Media type',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'Popular content retrieved successfully',
  })
  async getPopular(
    @Query('type') type?: 'movie' | 'tv',
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    const mediaType = type || 'movie';
    const pageNumber = page ? parseInt(page, 10) : 1;
    return this.mediaService.getPopular(mediaType, pageNumber);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search for movies and TV shows' })
  @ApiQuery({
    name: 'q',
    required: true,
    type: String,
    description: 'Search query',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
  })
  async search(
    @Query('q') query: string,
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    return this.mediaService.search(query, pageNumber);
  }

  @Get('genres')
  @ApiOperation({ summary: 'Get available genres' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['movie', 'tv'],
    description: 'Media type',
  })
  @ApiResponse({ status: 200, description: 'Genres retrieved successfully' })
  async getGenres(
    @Query('type') type?: 'movie' | 'tv',
  ): Promise<{ genres: Array<{ id: number; name: string }> }> {
    const mediaType = type || 'movie';
    return this.mediaService.getGenres(mediaType);
  }

  @Get('genre/:genreId')
  @ApiOperation({ summary: 'Get content by genre' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['movie', 'tv'],
    description: 'Media type',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiResponse({
    status: 200,
    description: 'Content by genre retrieved successfully',
  })
  async getByGenre(
    @Param('genreId') genreId: string,
    @Query('type') type?: 'movie' | 'tv',
    @Query('page') page?: string,
  ): Promise<TMDBResponse<TMDBMovie | TMDBTVShow>> {
    const mediaType = type || 'movie';
    const pageNumber = page ? parseInt(page, 10) : 1;
    const genreIdNumber = parseInt(genreId, 10);
    return this.mediaService.getByGenre(genreIdNumber, mediaType, pageNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get media by ID' })
  @ApiResponse({ status: 200, description: 'Media retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Media not found' })
  async getMediaById(@Param('id') id: string) {
    return this.mediaService.getMediaById(id);
  }
}
