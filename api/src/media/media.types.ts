import { MediaType } from '@prisma/client';

export interface MediaWithDetails {
  id: string;
  tmdbId: number;
  title: string;
  type: MediaType;
  posterUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MediaSearchParams {
  query?: string;
  type?: MediaType;
  genre?: number;
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  totalPages: number;
  totalResults: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
