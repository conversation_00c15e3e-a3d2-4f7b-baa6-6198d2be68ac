import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateAccountDto } from './dto/account.dto';
import { UserProfile } from './account.types';

@Injectable()
export class AccountService {
  constructor(private prisma: PrismaService) {}

  async getUserProfile(userId: string): Promise<UserProfile> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        account: true,
        _count: {
          select: {
            watched: true,
            lists: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      id: user.id,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      stats: {
        watchedCount: user._count.watched,
        listsCount: user._count.lists,
      },
      account: user.account
        ? {
            id: user.account.id,
            name: user.account.name,
            image: user.account.image || undefined,
            role: user.account.role,
            createdAt: user.account.createdAt,
            updatedAt: user.account.updatedAt,
          }
        : null,
    };
  }

  async updateUserProfile(userId: string, updateAccountDto: UpdateAccountDto) {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Update the account associated with the user
    const updatedAccount = await this.prisma.account.update({
      where: { userId: userId },
      data: {
        ...(updateAccountDto.name && { name: updateAccountDto.name }),
        ...(updateAccountDto.image && { image: updateAccountDto.image }),
      },
    });

    return updatedAccount;
  }

  async deleteUserAccount(userId: string) {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Delete user (account will be cascade deleted)
    return this.prisma.user.delete({
      where: { id: userId },
    });
  }
}
