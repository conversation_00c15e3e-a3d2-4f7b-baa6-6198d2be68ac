import {
  IsEmail,
  IsNotEmpty,
  IsString,
  Is<PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  IsUrl,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateAccountDto {
  @ApiProperty({ example: '<PERSON>', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'https://example.com/avatar.jpg', required: false })
  @IsOptional()
  @IsUrl()
  image?: string;
}
