import {
  Controller,
  Get,
  Patch,
  Body,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AccountService } from './account.service';
import { AuthGuard } from '../auth/auth.guard';
import { CurrentUser } from '../auth/auth.decorators';
import { UpdateAccountDto } from './dto/account.dto';
import { AuthUser } from '../auth/auth.types';

@ApiTags('account')
@Controller('account')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Get()
  @ApiOperation({ summary: 'Get user account information' })
  @ApiResponse({
    status: 200,
    description: 'Account information retrieved successfully',
  })
  async getAccount(@CurrentUser() user: AuthUser) {
    return this.accountService.getUserProfile(user.id);
  }

  @Patch()
  @ApiOperation({ summary: 'Update user account information' })
  @ApiResponse({ status: 200, description: 'Account updated successfully' })
  async updateAccount(
    @CurrentUser() user: AuthUser,
    @Body(ValidationPipe) updateAccountDto: UpdateAccountDto,
  ) {
    return this.accountService.updateUserProfile(user.id, updateAccountDto);
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get user profile (user + account info)' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  async getUserProfile(@CurrentUser() user: AuthUser) {
    return this.accountService.getUserProfile(user.id);
  }
}
