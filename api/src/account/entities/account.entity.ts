import { Role } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class Account {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  image?: string;

  @ApiProperty({ enum: Role })
  role: Role;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class UserProfile {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  stats: {
    watchedCount: number;
    listsCount: number;
  };

  @ApiProperty({ type: Account, nullable: true })
  account: Account | null;
}
