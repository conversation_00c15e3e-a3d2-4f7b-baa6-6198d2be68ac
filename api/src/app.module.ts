import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { TmdbModule } from './tmdb/tmdb.module';
import { MediaModule } from './media/media.module';
import { WatchedModule } from './watched/watched.module';
import { ListsModule } from './lists/lists.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { AccountModule } from './account/account.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    PrismaModule,
    AuthModule,
    TmdbModule,
    MediaModule,
    WatchedModule,
    ListsModule,
    AnalyticsModule,
    AccountModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
