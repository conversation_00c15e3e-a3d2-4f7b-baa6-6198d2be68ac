export interface AnalyticsOverview {
  totalWatched: number;
  totalMovies: number;
  totalTVShows: number;
  totalLists: number;
  averageRating: number;
  topGenres: GenreStats[];
}

export interface GenreStats {
  id: number;
  name: string;
  count: number;
  percentage: number;
}

export interface YearInReview {
  year: number;
  totalWatchedThisYear: number;
  moviesThisYear: number;
  tvShowsThisYear: number;
  topRatedThisYear: TopRatedItem[];
  monthlyBreakdown: MonthlyStats[];
  topGenresThisYear: GenreStats[];
  watchingStreak: number;
  firstWatchedThisYear?: string;
  lastWatchedThisYear?: string;
}

export interface TopRatedItem {
  id: string;
  title: string;
  rating: number;
  type: string;
  posterUrl?: string;
}

export interface MonthlyStats {
  month: string;
  count: number;
  movies: number;
  tvShows: number;
}

export interface WatchingPattern {
  dayOfWeek: DayStats[];
  timeOfDay: TimeStats[];
  monthlyTrend: MonthlyTrend[];
}

export interface DayStats {
  day: string;
  count: number;
}

export interface TimeStats {
  hour: number;
  count: number;
}

export interface MonthlyTrend {
  month: string;
  year: number;
  count: number;
}
