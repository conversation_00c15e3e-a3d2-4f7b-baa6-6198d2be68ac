import { ApiProperty } from '@nestjs/swagger';

export class GenreStatsDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  count: number;

  @ApiProperty()
  percentage: number;
}

export class AnalyticsOverviewDto {
  @ApiProperty()
  totalWatched: number;

  @ApiProperty()
  totalMovies: number;

  @ApiProperty()
  totalTVShows: number;

  @ApiProperty()
  totalLists: number;

  @ApiProperty()
  averageRating: number;

  @ApiProperty({ type: [GenreStatsDto] })
  topGenres: GenreStatsDto[];
}

export class TopRatedItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  rating: number;

  @ApiProperty()
  type: string;

  @ApiProperty({ required: false })
  posterUrl?: string;
}

export class MonthlyStatsDto {
  @ApiProperty()
  month: string;

  @ApiProperty()
  count: number;

  @ApiProperty()
  movies: number;

  @ApiProperty()
  tvShows: number;
}

export class YearInReviewDto {
  @ApiProperty()
  year: number;

  @ApiProperty()
  totalWatchedThisYear: number;

  @ApiProperty()
  moviesThisYear: number;

  @ApiProperty()
  tvShowsThisYear: number;

  @ApiProperty({ type: [TopRatedItemDto] })
  topRatedThisYear: TopRatedItemDto[];

  @ApiProperty({ type: [MonthlyStatsDto] })
  monthlyBreakdown: MonthlyStatsDto[];

  @ApiProperty({ type: [GenreStatsDto] })
  topGenresThisYear: GenreStatsDto[];

  @ApiProperty()
  watchingStreak: number;

  @ApiProperty({ required: false })
  firstWatchedThisYear?: string;

  @ApiProperty({ required: false })
  lastWatchedThisYear?: string;
}

export class DayStatsDto {
  @ApiProperty()
  day: string;

  @ApiProperty()
  count: number;
}

export class TimeStatsDto {
  @ApiProperty()
  hour: number;

  @ApiProperty()
  count: number;
}

export class MonthlyTrendDto {
  @ApiProperty()
  month: string;

  @ApiProperty()
  year: number;

  @ApiProperty()
  count: number;
}

export class WatchingPatternDto {
  @ApiProperty({ type: [DayStatsDto] })
  dayOfWeek: DayStatsDto[];

  @ApiProperty({ type: [TimeStatsDto] })
  timeOfDay: TimeStatsDto[];

  @ApiProperty({ type: [MonthlyTrendDto] })
  monthlyTrend: MonthlyTrendDto[];
}
