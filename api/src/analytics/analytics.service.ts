import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  AnalyticsOverview,
  GenreStats,
  YearInReview,
  TopRatedItem,
  MonthlyStats,
} from './analytics.types';

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getOverview(userId: string) {
    const [
      totalWatched,
      totalMovies,
      totalTVShows,
      totalLists,
      averageRating,
      topGenres,
    ] = await Promise.all([
      this.prisma.watched.count({ where: { userId } }),
      this.prisma.watched.count({
        where: { userId, media: { type: 'MOVIE' } },
      }),
      this.prisma.watched.count({
        where: { userId, media: { type: 'TV_SHOW' } },
      }),
      this.prisma.list.count({ where: { userId } }),
      this.prisma.watched.aggregate({
        where: { userId, rating: { not: null } },
        _avg: { rating: true },
      }),
      this.getTopGenres(userId),
    ]);

    return {
      totalWatched,
      totalMovies,
      totalTVShows,
      totalLists,
      averageRating: averageRating._avg.rating || 0,
      topGenres,
    };
  }

  async getYearInReview(userId: string, year?: number) {
    const targetYear = year || new Date().getFullYear();
    const startOfYear = new Date(targetYear, 0, 1);
    const endOfYear = new Date(targetYear + 1, 0, 1);

    const [
      totalWatchedThisYear,
      moviesThisYear,
      tvShowsThisYear,
      topRatedThisYear,
      watchTimeByMonth,
    ] = await Promise.all([
      this.prisma.watched.count({
        where: {
          userId,
          watchDate: { gte: startOfYear, lt: endOfYear },
        },
      }),
      this.prisma.watched.count({
        where: {
          userId,
          watchDate: { gte: startOfYear, lt: endOfYear },
          media: { type: 'MOVIE' },
        },
      }),
      this.prisma.watched.count({
        where: {
          userId,
          watchDate: { gte: startOfYear, lt: endOfYear },
          media: { type: 'TV_SHOW' },
        },
      }),
      this.prisma.watched.findMany({
        where: {
          userId,
          watchDate: { gte: startOfYear, lt: endOfYear },
          rating: { not: null },
        },
        include: { media: true },
        orderBy: { rating: 'desc' },
        take: 10,
      }),
      this.getWatchTimeByMonth(userId, targetYear),
    ]);

    return {
      year: targetYear,
      totalWatchedThisYear,
      moviesThisYear,
      tvShowsThisYear,
      topRatedThisYear,
      watchTimeByMonth,
    };
  }

  async getTopMovies(userId: string, limit: number = 10) {
    return this.prisma.watched.findMany({
      where: {
        userId,
        media: { type: 'MOVIE' },
        rating: { not: null },
      },
      include: { media: true },
      orderBy: { rating: 'desc' },
      take: limit,
    });
  }

  async getTopShows(userId: string, limit: number = 10) {
    return this.prisma.watched.findMany({
      where: {
        userId,
        media: { type: 'TV_SHOW' },
        rating: { not: null },
      },
      include: { media: true },
      orderBy: { rating: 'desc' },
      take: limit,
    });
  }

  async getWatchTime(userId: string) {
    const movieStats = await this.prisma.watched.groupBy({
      by: ['userId'],
      where: {
        userId,
        media: { type: 'MOVIE' },
      },
      _count: { id: true },
    });

    const tvStats = await this.prisma.watched.groupBy({
      by: ['userId'],
      where: {
        userId,
        media: { type: 'TV_SHOW' },
      },
      _count: { id: true },
    });

    // Estimate watch time (assuming 2h per movie, 45min per TV episode)
    const movieTime = (movieStats[0]?._count.id || 0) * 120; // 2 hours in minutes
    const tvTime = (tvStats[0]?._count.id || 0) * 45; // 45 minutes per episode

    return {
      movies: {
        count: movieStats[0]?._count.id || 0,
        estimatedMinutes: movieTime,
      },
      tvShows: {
        count: tvStats[0]?._count.id || 0,
        estimatedMinutes: tvTime,
      },
      total: {
        count: (movieStats[0]?._count.id || 0) + (tvStats[0]?._count.id || 0),
        estimatedMinutes: movieTime + tvTime,
      },
    };
  }

  private async getTopGenres(userId: string) {
    // This would require genre data in the media table
    // For now, return empty array - can be implemented when genre data is available
    return [];
  }

  private async getWatchTimeByMonth(userId: string, year: number) {
    const months = Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      const startOfMonth = new Date(year, i, 1);
      const endOfMonth = new Date(year, i + 1, 1);

      return {
        month,
        name: new Date(year, i).toLocaleString('default', { month: 'long' }),
        startDate: startOfMonth,
        endDate: endOfMonth,
      };
    });

    const watchCountsByMonth = await Promise.all(
      months.map(async ({ month, name, startDate, endDate }) => {
        const count = await this.prisma.watched.count({
          where: {
            userId,
            watchDate: { gte: startDate, lt: endDate },
          },
        });

        return { month, name, count };
      }),
    );

    return watchCountsByMonth;
  }
}
