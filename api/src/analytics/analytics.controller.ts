import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AnalyticsService } from './analytics.service';
import { AuthGuard } from '../auth/auth.guard';
import { CurrentUser } from '../auth/auth.decorators';
import { AuthUser } from '../auth/auth.types';

@ApiTags('analytics')
@Controller('analytics')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('overview')
  @ApiOperation({ summary: 'Get analytics overview' })
  @ApiResponse({
    status: 200,
    description: 'Analytics overview retrieved successfully',
  })
  async getOverview(@CurrentUser() user: AuthUser) {
    return this.analyticsService.getOverview(user.id);
  }

  @Get('year-in-review')
  @ApiOperation({ summary: 'Get year in review analytics' })
  @ApiQuery({
    name: 'year',
    required: false,
    type: Number,
    description: 'Year (defaults to current year)',
  })
  @ApiResponse({
    status: 200,
    description: 'Year in review retrieved successfully',
  })
  async getYearInReview(
    @CurrentUser() user: AuthUser,
    @Query('year') year?: string,
  ) {
    const targetYear = year ? parseInt(year, 10) : undefined;
    return this.analyticsService.getYearInReview(user.id, targetYear);
  }

  @Get('top-movies')
  @ApiOperation({ summary: 'Get top-rated movies' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Limit results',
  })
  @ApiResponse({
    status: 200,
    description: 'Top movies retrieved successfully',
  })
  async getTopMovies(
    @CurrentUser() user: AuthUser,
    @Query('limit') limit?: string,
  ) {
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    return this.analyticsService.getTopMovies(user.id, limitNumber);
  }

  @Get('top-shows')
  @ApiOperation({ summary: 'Get top-rated TV shows' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Limit results',
  })
  @ApiResponse({ status: 200, description: 'Top shows retrieved successfully' })
  async getTopShows(
    @CurrentUser() user: AuthUser,
    @Query('limit') limit?: string,
  ) {
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    return this.analyticsService.getTopShows(user.id, limitNumber);
  }

  @Get('watch-time')
  @ApiOperation({ summary: 'Get watch time statistics' })
  @ApiResponse({
    status: 200,
    description: 'Watch time statistics retrieved successfully',
  })
  async getWatchTime(@CurrentUser() user: AuthUser) {
    return this.analyticsService.getWatchTime(user.id);
  }
}
