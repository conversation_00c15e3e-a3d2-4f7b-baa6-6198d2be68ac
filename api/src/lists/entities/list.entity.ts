import { MediaType } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class List {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class ListItem {
  @ApiProperty()
  id: string;

  @ApiProperty()
  listId: string;

  @ApiProperty()
  mediaId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  media: {
    id: string;
    tmdbId: number;
    title: string;
    type: MediaType;
    posterUrl?: string;
  };
}

export class ListWithItems extends List {
  @ApiProperty({ type: [ListItem] })
  items: ListItem[];

  @ApiProperty()
  itemCount: number;
}

export class ListSummary {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  itemCount: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
