import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { AuthDto, AuthResponseDto } from './dto/auth.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('sign-up')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User registered successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email already exists',
  })
  signup(@Body(ValidationPipe) dto: AuthDto): Promise<AuthResponseDto> {
    return this.authService.signup(dto);
  }

  @Post('sign-in')
  @ApiOperation({ summary: 'Sign in user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Signed in successfully!',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Invalid credentials',
  })
  signin(@Body(ValidationPipe) dto: AuthDto): Promise<AuthResponseDto> {
    return this.authService.signin(dto);
  }
}
