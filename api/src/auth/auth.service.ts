import { AuthDto, AuthResponseDto } from './dto/auth.dto';
import { ConfigService } from '@nestjs/config';
import {
  ConflictException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from '../prisma/prisma.service';
import * as argon from 'argon2';
import { Role } from '@prisma/client';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly jwt: JwtService,
  ) {}

  async signup(dto: AuthDto): Promise<AuthResponseDto> {
    try {
      dto.password = await argon.hash(dto.password);
      const result = await this.prisma.$transaction(async (tx) => {
        const user = await tx.user.create({
          data: {
            email: dto.email,
            password: dto.password,
            role: Role.USER,
          },
        });

        const account = await tx.account.create({
          data: {
            userId: user.id,
            name: dto.email.split('@')[0],
          },
        });

        return { user, account };
      });

      return await this.signToken(result.user.id);
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002')
          throw new ConflictException('An account with this email exists!');
      }
      throw error;
    }
  }

  async signin(dto: AuthDto): Promise<AuthResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { email: dto.email },
    });

    if (!user) {
      throw new ForbiddenException('Credentials incorrect!');
    }

    const pwMatches = await argon.verify(user.password, dto.password);
    if (!pwMatches) {
      throw new ForbiddenException('Credentials incorrect!');
    }

    return await this.signToken(user.id);
  }

  async signToken(id: string): Promise<AuthResponseDto> {
    const payload = { sub: id };
    const token = await this.jwt.signAsync(payload, {
      expiresIn: '15m',
      secret: this.config.get('JWT_SECRET'),
    });

    return { access_token: token };
  }
}
