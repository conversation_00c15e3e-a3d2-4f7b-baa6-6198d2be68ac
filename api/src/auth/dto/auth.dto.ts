import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Min<PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AuthUser } from '../entities/auth.entity';

export class AuthDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password123', minLength: 8 })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;
}

export class AuthResponseDto {
  @ApiProperty()
  access_token: string;
}
