import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { User, Role } from '@prisma/client';

interface RequestWithUser extends Request {
  user: User;
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<Role[]>('roles', context.getHandler());

    if (!roles?.length) return true;
    const { user } = context.switchToHttp().getRequest<RequestWithUser>();
    if (!user?.role) return false;

    return roles.includes(user.role);
  }
}
