# Database Configuration
DATABASE_URL="file:./dev.db"

# TMDB API Configuration
TMDB_API_KEY="your_tmdb_api_key_here"
TMDB_BASE_URL="https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL="https://image.tmdb.org/t/p"

# Trakt.tv API Configuration
TRAKT_CLIENT_ID="your_trakt_api_client_id_here"
TRAKT_CLIENT_SECRET="your_trakt_api_client_secret_here"

# Server Configuration
PORT=4020
NODE_ENV="development"
CORS_ORIGIN="http://localhost:3000"

# Better Auth Configuration
BETTER_AUTH_URL="http://localhost:4020"
BETTER_AUTH_SECRET="your-super-secret-key-here-change-this-in-production"
JWT_SECRET="your-super-secret-jwt-key-change-in-production-please-make-it-at-least-32-chars"

# Trusted Origins (comma-separated list for CORS)
TRUSTED_ORIGINS="http://localhost:3000,http://localhost:3001"

# Cross-domain configuration (for production)
# CROSS_DOMAIN_ORIGIN=".yourdomain.com"

# Social Authentication Providers (optional)
# GitHub OAuth
# GITHUB_CLIENT_ID="your-github-client-id"
# GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google OAuth
# GOOGLE_CLIENT_ID="your-google-client-id"
# GOOGLE_CLIENT_SECRET="your-google-client-secret"
